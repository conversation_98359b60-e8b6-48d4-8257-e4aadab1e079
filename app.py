#!/usr/bin/env python3
import asyncio
import signal
import sys
import yaml
from typing import Dict, Any

from logger import get_logger
from core.connection import WebSocketServer

logger = get_logger(__name__)


def load_config(config_path: str = "config/config.yaml") -> Dict[str, Any]:
    """Load configuration from YAML file"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            if not isinstance(config, dict):
                logger.critical(f"Invalid configuration format in {config_path}.")
                sys.exit(1)
            return config
    except FileNotFoundError:
        logger.critical(f"Config file {config_path} not found")
        sys.exit(1)
    except yaml.YAMLError as e:
        logger.critical(f"Error parsing config file {config_path}: {e}")
        sys.exit(1)
    except Exception as e:
        logger.critical(f"Unexpected error loading config file {config_path}: {e}")
        sys.exit(1)

async def main():
    config = load_config()
    server = WebSocketServer(config)
    
    # Set up signal handlers for graceful shutdown
    def signal_handler():
        logger.info("[SIGNAL] Received shutdown signal (Ctrl+C)")
        asyncio.create_task(server.shutdown())
    
    # Register signal handlers
    loop = asyncio.get_running_loop()
    for sig in (signal.SIGTERM, signal.SIGINT):
        loop.add_signal_handler(sig, signal_handler)
    
    logger.info("Starting WebSocket server...")
    
    try:
        await server.start_server()
    except Exception as e:
        logger.error(f"Server error: {e}")
        await server.shutdown()
        raise

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        # This should not happen due to signal handling, but just in case
        logger.info("[EXIT] Server stopped")
    except Exception as e:
        logger.error(f"[EXIT] Server error: {e}")
    finally:
        logger.info("[EXIT] Application terminated")
