与 dazi 配合的后端服务。本项目会启动一个 WebSocket 服务监听，手机客户端连接上以后，会发送语音或文本消息，服务端实时用语音和文本回复。

架构：

* 程序入口：app.py 主程序，只负责加载配置、启动WebSocket服务器和处理Ctrl-C退出
* 网络层：connection.py 负责处理WebSocket连接，包括握手、消息解析和分发
* 协议层：protocol.py 负责定义WebSocket通信协议，包括消息类型、格式等
* 事件层：agent_event_service.py 维护一个事件总线，以事件驱动的方式，客户端发来的语音或者文本消息，变成事件放到总线上，然后多个LangGraph的主动式Agent来消费这些事件。Agent之间相互协作，分工如下：
    ** FastReplyAgent：收到语音数据后，内部作为多模态的工作流直接处理语音数据。内部调用自己的多模态LLM，以语音为输入，以语音为输出，最快回答客户端上用户说的话。
    ** SearchAgent：同样也消费这份语音数据。会对语音数据进行深度分析，调用Gemini 2.5 Pro Thinking+Search+URL 
  Context搜索相关资料，速度比FastReplyAgent慢，但当它搜集完资料、整理出一个回答以后，会发给FastReplyAgent，FastReplyAgent接着会调整后续说的话。
    ** VadAgent：如果在FastReplyAgent说话期间，用户发来了新的语音，判断一下用户是不是要打断对话，如果是，则通知FastReplyAgent停下来。
