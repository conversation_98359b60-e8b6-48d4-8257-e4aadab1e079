from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Callable, AsyncGenerator
import logging


class LLMBase(ABC):
    """Base class for LLM providers with streaming support."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize LLM provider with configuration."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        self._cancelled = False
    
    @abstractmethod
    async def generate_stream(
        self,
        system_prompt: str,
        user_context: str,
        file_path: Optional[str] = None,
        callback: Optional[Callable[[str, bool], bool]] = None
    ) -> AsyncGenerator[str, None]:
        """
        Generate streaming response from LLM.
        
        Args:
            system_prompt: System prompt for LLM
            user_context: User message/context
            file_path: Optional file path (audio, image, etc.)
            callback: Optional callback function called for each chunk
                     Returns True to continue, False to cancel
        
        Yields:
            str: Streaming text chunks from LLM
        """
        pass
    
    @abstractmethod
    async def generate(
        self,
        system_prompt: str,
        user_context: str,
        file_path: Optional[str] = None
    ) -> str:
        """
        Generate non-streaming response from LLM.
        
        Args:
            system_prompt: System prompt for LLM
            user_context: User message/context
            file_path: Optional file path (audio, image, etc.)
        
        Returns:
            str: Complete response text
        """
        pass
    
    async def generate_stream_with_callback(
        self,
        system_prompt: str,
        user_context: str,
        file_path: Optional[str] = None,
        callback: Optional[Callable[[str, bool], bool]] = None
    ) -> str:
        """
        Generate response with callback function support for streaming.
        
        Args:
            system_prompt: System prompt for LLM
            user_context: User message/context
            file_path: Optional file path (audio, image, etc.)
            callback: Callback function called for each chunk (chunk, is_final) -> continue_flag
        
        Returns:
            str: Complete response text
        """
        self._cancelled = False
        complete_response = ""
        
        try:
            async for chunk in self.generate_stream(system_prompt, user_context, file_path, callback):
                if self._cancelled:
                    self.logger.info("Generation cancelled by callback")
                    break
                
                complete_response += chunk
                
                # Call callback if provided
                if callback:
                    should_continue = callback(chunk, False)
                    if not should_continue:
                        self._cancelled = True
                        break
            
            # Call callback with final flag if not cancelled
            if not self._cancelled and callback:
                callback("", True)  # Empty chunk with is_final=True
                
        except Exception as e:
            self.logger.error(f"Error in LLM generation: {str(e)}")
            if callback:
                callback(f"Error: {str(e)}", True)
            raise
        
        return complete_response
    
    def cancel_generation(self):
        """Cancel ongoing generation."""
        self._cancelled = True
        self.logger.info("LLM generation cancelled")
    
