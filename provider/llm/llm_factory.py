import importlib
from typing import Dict, Any
from .llm_base import LLMBase
import logging

logger = logging.getLogger(__name__)


class LLMFactory:
    """Factory class for creating LLM provider instances"""
    
    def create_audio_llm_provider(self, config: Dict[str, Any]) -> LLMBase:
        """
        Create an audio LLM provider based on configuration.
        
        Args:
            config: Configuration dictionary
            
        Returns:
            LLMBase instance
            
        Raises:
            ValueError: If provider not found or creation fails
        """
        # Get specified audio LLM provider from config
        llm_config = config.get('llm', {})
        audio_llm_config = llm_config.get('audio_llm', {})
        provider_class_name = audio_llm_config.get('provider')
        
        if not provider_class_name:
            raise ValueError("No audio LLM provider specified in configuration")
        
        try:
            # Try to get the class from current module's scope
            current_module = importlib.import_module('provider.llm')
            provider_class = getattr(current_module, provider_class_name)
            
            # Verify it's a subclass of LLMBase
            if not issubclass(provider_class, LLMBase):
                raise ValueError(f"{provider_class_name} is not a valid LLM provider (must inherit from LLMBase)")
            
            provider = provider_class(config)
            logger.info(f"Created audio LLM provider: {provider_class_name}")
            return provider
            
        except AttributeError:
            raise ValueError(f"Provider class '{provider_class_name}' not found")
        except Exception as e:
            raise ValueError(f"Failed to create provider {provider_class_name}: {e}")


# Global factory instance
llm_factory = LLMFactory()