import openai
import base64
from typing import Dict, Any, Optional, Callable, AsyncGenerator
from .llm_base import LLMBase


class OpenAIAudioProvider(LLMBase):
    """OpenAI audio provider with streaming support for audio input processing."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize OpenAI provider with configuration."""
        super().__init__(config)
        
        openai_config = config.get('openai', {})
        self.api_key = openai_config.get('api_key')
        self.model = openai_config.get('model', 'gpt-4o-audio-preview')
        
        if not self.api_key:
            raise ValueError("OpenAI API key not found in configuration")
        
        # Initialize OpenAI client
        self.client = openai.OpenAI(api_key=self.api_key)
        
        self.logger.info(f"OpenAI provider initialized with model: {self.model}")
    
    async def generate_stream(
        self,
        system_prompt: str,
        user_context: str,
        file_path: Optional[str] = None,
        callback: Optional[Callable[[str, bool], bool]] = None
    ) -> AsyncGenerator[str, None]:
        """Generate streaming response from OpenAI."""
        try:
            # Prepare messages
            messages = [
                {"role": "system", "content": system_prompt}
            ]

            # Prepare user message content
            user_content = []

            # Add text content
            if user_context:
                user_content.append({"type": "text", "text": user_context})

            # If file_path is provided, add audio input
            if file_path:
                try:
                    with open(file_path, 'rb') as audio_file:
                        audio_data = audio_file.read()
                        audio_b64 = base64.b64encode(audio_data).decode('utf-8')

                    # Add audio input to user content
                    user_content.append({
                        "type": "input_audio",
                        "input_audio": {
                            "data": audio_b64,
                            "format": "wav"
                        }
                    })

                    self.logger.info(f"Audio file loaded for OpenAI analysis: {file_path}")

                except Exception as e:
                    self.logger.error(f"Failed to load audio file: {str(e)}")
                    yield f"音频文件加载失败：{str(e)}"
                    return

            # Add user message
            messages.append({
                "role": "user",
                "content": user_content if len(user_content) > 1 else user_context
            })

            # Determine if we need audio modalities
            modalities = ["text", "audio"] if file_path else ["text"]

            # Create streaming response
            stream = self.client.chat.completions.create(
                model=self.model,
                modalities=modalities,
                response_format={"type": "text"},
                messages=messages,
                temperature=0.3,
                stream=True
            )
            
            for chunk in stream:
                if self._cancelled:
                    break
                    
                if chunk.choices[0].delta.content is not None:
                    content = chunk.choices[0].delta.content
                    yield content
                    
        except Exception as e:
            self.logger.error(f"Error in OpenAI streaming: {str(e)}")
            yield f"生成回复时出错：{str(e)}"
    
