from google import genai
from google.genai import types
from typing import Dict, Any, Optional, Callable, AsyncGenerator
from .llm_base import LLMBase


class GeminiProvider(LLMBase):
    """Gemini provider with streaming support."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Gemini provider with configuration."""
        super().__init__(config)
        
        gemini_config = config.get('gemini', {})
        self.api_key = gemini_config.get('api_key')
        self.model = gemini_config.get('model', 'gemini-2.5-flash')
        
        if not self.api_key:
            raise ValueError("Gemini API key not found in configuration")
        
        # Initialize Gemini client
        self.client = genai.Client(api_key=self.api_key)

        self.logger.info(f"Gemini provider initialized with model: {self.model}")
    
    async def generate_stream(
        self,
        system_prompt: str,
        user_context: str,
        file_path: Optional[str] = None,
        callback: Optional[Callable[[str, bool], bool]] = None
    ) -> AsyncGenerator[str, None]:
        """Generate streaming response from <PERSON>."""
        try:
            # Prepare content
            contents = []

            # If file_path is provided, upload it
            if file_path:
                try:
                    uploaded_file = self.client.files.upload(file=file_path)
                    contents = [user_context, uploaded_file]
                    self.logger.info(f"File uploaded for Gemini analysis: {file_path}")
                except Exception as e:
                    self.logger.error(f"Failed to upload file to Gemini: {str(e)}")
                    yield f"文件上传失败：{str(e)}"
                    return
            else:
                contents = user_context

            # Create generation config with system instruction
            generation_config = types.GenerateContentConfig(
                system_instruction=system_prompt,
                thinking_config=types.ThinkingConfig(thinking_budget=0)  # Disable thinking
            )

            # Create streaming response
            response = self.client.models.generate_content_stream(
                model=self.model,
                contents=contents,
                config=generation_config
            )
            
            for chunk in response:
                if self._cancelled:
                    break
                    
                if hasattr(chunk, 'text') and chunk.text:
                    yield chunk.text
                    
        except Exception as e:
            self.logger.error(f"Error in Gemini streaming: {str(e)}")
            yield f"生成回复时出错：{str(e)}"
    
