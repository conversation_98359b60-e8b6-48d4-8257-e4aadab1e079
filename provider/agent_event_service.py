import asyncio
import json
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
from .base_service import BaseService
from .audio_frontend.vad import SileroVAD
from .audio_frontend.audio_file_utils import AudioFileProcessor
from .agents.fast_reply_agent import FastReplyAgent
from .agents.search_agent import SearchAgent
from .agents.vad_agent import VadAgent


@dataclass
class Event:
    """Event data structure for the event bus"""
    id: str
    type: str  # 'audio', 'text', 'agent_message', 'interrupt'
    data: Any
    timestamp: datetime
    session_id: str
    metadata: Dict[str, Any] = None

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


class EventBus:
    """Event bus for inter-agent communication"""
    
    def __init__(self):
        self.subscribers = {}
        self.logger = None
    
    def set_logger(self, logger):
        self.logger = logger
    
    def subscribe(self, event_type: str, callback):
        """Subscribe to an event type"""
        if event_type not in self.subscribers:
            self.subscribers[event_type] = []
        self.subscribers[event_type].append(callback)
        if self.logger:
            self.logger.info(f"Subscribed to event type: {event_type}")
    
    async def publish(self, event: Event):
        """Publish an event to all subscribers"""
        if self.logger:
            self.logger.debug(f"Publishing event: {event.type} - {event.id}")
        
        if event.type in self.subscribers:
            tasks = []
            for callback in self.subscribers[event.type]:
                tasks.append(asyncio.create_task(callback(event)))
            
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)


class AgentEventService(BaseService):
    """Multi-agent service with LangGraph-based agent architecture"""
    
    def __init__(self, config: Dict[str, Any], connection_handler=None):
        super().__init__(config, connection_handler)
        
        # Initialize event bus
        self.event_bus = EventBus()
        self.event_bus.set_logger(self.logger)
        
        # Initialize VAD and audio processor
        self.vad = SileroVAD(config)
        audio_config = config.get('audio', {})
        output_dir = audio_config.get('output_dir', 'log/audio')
        self.audio_saver = AudioFileProcessor(output_dir)
        
        # Initialize LangGraph agents with event callbacks
        self.fast_reply_agent = FastReplyAgent(config, connection_handler)
        self.search_agent = SearchAgent(config, self._handle_search_result)
        self.vad_agent = VadAgent(config, self._handle_interruption)
        
        # Set loggers for all agents
        self.fast_reply_agent.set_logger(self.logger)
        self.search_agent.set_logger(self.logger)
        self.vad_agent.set_logger(self.logger)
    
    async def initialize(self) -> bool:
        """Initialize the agent event service and all LangGraph agents"""
        try:
            # Initialize all LangGraph agents
            success = await self.fast_reply_agent.initialize()
            success &= await self.search_agent.initialize()
            success &= await self.vad_agent.initialize()
            
            if success:
                self.logger.info("AgentEventService initialized successfully with LangGraph agents")
            else:
                self.logger.error("Failed to initialize some LangGraph agents")
            
            return success
            
        except Exception as e:
            self.logger.error(f"AgentEventService initialization error: {e}")
            return False
    
    async def _handle_search_result(self, event_data: Dict[str, Any]):
        """Handle search results from SearchAgent"""
        try:
            session_id = event_data.get('session_id', '')
            result = event_data.get('data', {}).get('result', '')
            
            if result and session_id == self.session_id:
                # Send enhanced response from search results
                await self.fast_reply_agent.update_with_search_result(result, session_id)
                
        except Exception as e:
            self.logger.error(f"Error handling search result: {e}")
    
    async def _handle_interruption(self, event_data: Dict[str, Any]):
        """Handle interruption signals from VadAgent"""
        try:
            session_id = event_data.get('session_id', '')
            
            if session_id == self.session_id:
                # Notify FastReplyAgent of interruption
                await self.fast_reply_agent.handle_interrupt(session_id)
                
        except Exception as e:
            self.logger.error(f"Error handling interruption: {e}")
    
    async def handle_audio_input(self, audio_data: bytes) -> None:
        """Process audio data with VAD and multi-agent handling"""
        # Check for interruption using VadAgent
        fast_reply_speaking = hasattr(self.fast_reply_agent, 'is_speaking') and getattr(self.fast_reply_agent, 'is_speaking', False)
        vad_result = await self.vad_agent.process_audio_frame(audio_data, self.session_id, fast_reply_speaking)
        
        if vad_result.get('interruption_detected', False):
            return
        
        # Normal VAD processing for speech end detection
        speech_vad_result = self.vad.process_audio_frame(audio_data)
        
        if speech_vad_result['speech_end']:
            # Speech ended, process audio
            opus_packets = speech_vad_result['audio_buffer']
            await self._process_audio_packets(opus_packets, "Speech ended")
    
    async def handle_text_input(self, text: str) -> None:
        """Handle text input through LangGraph agents"""
        self.logger.info(f"Received text input: {text}")
        
        try:
            # Process text through FastReplyAgent
            asyncio.create_task(
                self.fast_reply_agent.process_text_event(text, self.session_id)
            )
            
            # Also process through SearchAgent for enhanced responses
            asyncio.create_task(
                self.search_agent.process_text_event(text, self.session_id)
            )
            
        except Exception as e:
            self.logger.error(f"Error handling text input: {e}")
    
    async def handle_client_audio_complete(self) -> None:
        """Handle client audio complete signal"""
        if self.vad.audio_packets and len(self.vad.audio_packets) > 10:
            await self._process_audio_packets(self.vad.audio_packets, "Client audio complete")
            self.vad.reset()
        else:
            self.logger.warning("Client audio complete called but no audio data available")
    
    async def _process_audio_packets(self, opus_packets, log_prefix: str) -> None:
        """Process audio packets through LangGraph agents"""
        if not opus_packets or len(opus_packets) <= 10:
            self.logger.warning(f"{log_prefix} - speech too short, ignoring")
            return
        
        self.logger.info(f"{log_prefix} - processing {len(opus_packets)} Opus packets")
        
        # Save as MP3 file
        mp3_file = self.audio_saver.save_audio_as_mp3(opus_packets, self.session_id)
        if mp3_file:
            self.logger.info(f"{log_prefix} - audio saved to: {mp3_file}")
            
            try:
                # Process audio through FastReplyAgent (quick response)
                asyncio.create_task(
                    self.fast_reply_agent.process_audio_event(mp3_file, self.session_id)
                )
                
                # Process audio through SearchAgent (deep analysis)
                asyncio.create_task(
                    self.search_agent.process_audio_event(mp3_file, self.session_id)
                )
                
            except Exception as e:
                self.logger.error(f"Error processing audio through agents: {e}")
        else:
            self.logger.error(f"{log_prefix} - failed to save MP3 file")
    
    async def cleanup(self) -> None:
        """Clean up resources"""
        if self.session_id:
            self.monitor.end_session(self.session_id, completed=True)
        
        self.vad.reset()
        self.logger.info("AgentEventService cleaned up resources")