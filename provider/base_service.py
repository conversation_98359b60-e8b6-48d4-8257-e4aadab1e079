#!/usr/bin/env python3
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, AsyncGenerator
from logger import get_logger
from core.protocol import protocol_handler, MessageType, TTSState
from quality import quality_monitor

class BaseService(ABC):
    """Base class for all service providers"""
    
    def __init__(self, config: Dict[str, Any], connection_handler=None):
        self.config = config
        self.session_id: Optional[str] = None
        self.websocket = None
        self.connection_handler = connection_handler
        self.logger = get_logger(self.__class__.__name__)
        self.monitor = quality_monitor
        
    async def set_websocket(self, websocket):
        """Set the WebSocket connection for sending responses"""
        self.websocket = websocket
        
    async def set_session_id(self, session_id: str):
        """Set the session ID"""
        self.session_id = session_id
        # Start monitoring when session is set
        if self.session_id:
            service_type = self.__class__.__name__.replace('Service', '').lower()
            self.monitor.start_session(self.session_id, service_type)
        
    @abstractmethod
    async def initialize(self) -> bool:
        """Initialize the service. Return True if successful."""
        pass
        
    @abstractmethod
    async def handle_audio_input(self, audio_data: bytes) -> None:
        """Handle audio input from client"""
        pass
        
    @abstractmethod
    async def handle_text_input(self, text: str) -> None:
        """Handle text input from client"""
        pass
        
    @abstractmethod
    async def cleanup(self) -> None:
        """Cleanup resources when connection closes"""
        pass
        
    async def send_text_response(self, text: str):
        """Send text response to client (STT + TTS start)"""
        if self.websocket and self.session_id:
            # Record first character received if this is the first response
            self.monitor.record_first_char_received(self.session_id)
            
            # Send STT message
            stt_msg = protocol_handler.create_stt_message(self.session_id, text)
            self.logger.debug(f"[BASE SERVICE] Sending STT message: {stt_msg}")
            await self.websocket.send(protocol_handler.serialize_message(stt_msg))
            
            # Automatically send TTS start message (matching reference project behavior)
            tts_start_msg = protocol_handler.create_tts_message(self.session_id, TTSState.START)
            self.logger.debug(f"[BASE SERVICE] Sending TTS start message: {tts_start_msg}")
            await self.websocket.send(protocol_handler.serialize_message(tts_start_msg))
            
    async def send_audio_response(self, audio_data: bytes, text: str = None):
        """Send audio response to client"""
        if self.websocket and self.session_id:
            # Record first character received if this is the first response
            self.monitor.record_first_char_received(self.session_id)
            
            # Send TTS sentence start
            tts_start = protocol_handler.create_tts_message(self.session_id, TTSState.SENTENCE_START, text)
            await self.websocket.send(protocol_handler.serialize_message(tts_start))
            
            # Send audio data
            await self.websocket.send(audio_data)
            
            # Send TTS sentence end
            tts_stop = protocol_handler.create_tts_message(self.session_id, TTSState.SENTENCE_END, text)
            await self.websocket.send(protocol_handler.serialize_message(tts_stop))