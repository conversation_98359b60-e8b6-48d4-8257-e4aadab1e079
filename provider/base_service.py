#!/usr/bin/env python3
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, AsyncGenerator
from logger import get_logger
from quality import quality_monitor

class BaseService(ABC):
    """Base class for all service providers"""
    
    def __init__(self, config: Dict[str, Any], connection_handler=None):
        self.config = config
        self.session_id: Optional[str] = None
        self.websocket = None
        self.connection_handler = connection_handler
        self.logger = get_logger(self.__class__.__name__)
        self.monitor = quality_monitor
        
    async def set_websocket(self, websocket):
        """Set the WebSocket connection for sending responses"""
        self.websocket = websocket
        
    async def set_session_id(self, session_id: str):
        """Set the session ID"""
        self.session_id = session_id
        # Start monitoring when session is set
        if self.session_id:
            service_type = self.__class__.__name__.replace('Service', '').lower()
            self.monitor.start_session(self.session_id, service_type)
        
    @abstractmethod
    async def initialize(self) -> bool:
        """Initialize the service. Return True if successful."""
        pass
        
    @abstractmethod
    async def handle_audio_input(self, audio_data: bytes) -> None:
        """Handle audio input from client"""
        pass
        
    @abstractmethod
    async def handle_text_input(self, text: str) -> None:
        """Handle text input from client"""
        pass
        
    @abstractmethod
    async def handle_client_audio_complete(self) -> None:
        """Handle client audio complete - called when client sends stop signal indicating they finished speaking"""
        pass
        
    @abstractmethod
    async def cleanup(self) -> None:
        """Cleanup resources when connection closes"""
        pass
        
