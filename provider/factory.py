from typing import Dict, Any, Optional
from . import BaseService, EchoService, GeminiLiveService
from logger import get_logger

class ServiceFactory:
    """Factory class for creating service instances"""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self._service_registry = {
            "echo": EchoService,
            "gemini_live": GeminiLiveService,
        }
    
    def register_service(self, service_type: str, service_class: type):
        """Register a new service type"""
        self._service_registry[service_type] = service_class
        self.logger.info(f"Registered service type: {service_type}")
    
    def get_available_services(self) -> list:
        """Get list of available service types"""
        return list(self._service_registry.keys())
    
    def create_service(self, service_type: str, config: Dict[str, Any], connection_handler=None) -> Optional[BaseService]:
        """Create a service instance based on type"""
        if service_type not in self._service_registry:
            self.logger.error(f"Unknown service type: {service_type}")
            available = ", ".join(self.get_available_services())
            self.logger.error(f"Available service types: {available}")
            return None
        
        try:
            service_class = self._service_registry[service_type]
            service_instance = service_class(config, connection_handler)
            self.logger.info(f"Created service instance: {service_type}")
            return service_instance
        except Exception as e:
            self.logger.error(f"Failed to create service {service_type}: {e}")
            return None
    
    async def initialize_service(self, service: BaseService, websocket, session_id: str) -> bool:
        """Initialize a service instance with websocket and session"""
        try:
            await service.set_websocket(websocket)
            await service.set_session_id(session_id)
            
            success = await service.initialize()
            if success:
                self.logger.info(f"Service {service.__class__.__name__} initialized successfully")
                return True
            else:
                self.logger.error(f"Failed to initialize service {service.__class__.__name__}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error initializing service {service.__class__.__name__}: {e}")
            return False

# Global factory instance
service_factory = ServiceFactory()