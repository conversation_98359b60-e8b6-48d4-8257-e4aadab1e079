import os
import numpy as np
import opuslib_next
from pathlib import Path
from typing import List, <PERSON><PERSON>
from logger import get_logger

logger = get_logger(__name__)

def audio_to_data(audio_file_path: str, is_opus: bool = True) -> Tuple[List[bytes], float]:
    """
    将音频文件转换为Opus数据包列表，参考yuyan-server的实现
    
    Args:
        audio_file_path: 音频文件路径
        is_opus: 是否输出为Opus格式
        
    Returns:
        Tuple[List[bytes], float]: (音频数据包列表, 音频时长秒数)
    """
    try:
        # 导入pydub用于音频处理
        from pydub import AudioSegment
    except ImportError:
        logger.error("[AUDIO UTILS] pydub is required but not installed. Please run: pip install pydub")
        raise
    
    try:
        # 获取文件后缀名
        file_type = os.path.splitext(audio_file_path)[1]
        if file_type:
            file_type = file_type.lstrip(".")
        
        # 读取音频文件，-nostdin 参数：不要从标准输入读取数据，否则FFmpeg会阻塞
        audio = AudioSegment.from_file(
            audio_file_path, format=file_type, parameters=["-nostdin"]
        )

        # 转换为单声道/16kHz采样率/16位小端编码（确保与编码器匹配）
        audio = audio.set_channels(1).set_frame_rate(16000).set_sample_width(2)

        # 音频时长(秒)
        duration = len(audio) / 1000.0

        # 获取原始PCM数据（16位小端）
        raw_data = audio.raw_data

        # 初始化Opus编码器
        encoder = opuslib_next.Encoder(16000, 1, opuslib_next.APPLICATION_AUDIO)

        # 编码参数
        frame_duration = 60  # 60ms per frame
        frame_size = int(16000 * frame_duration / 1000)  # 960 samples/frame

        datas = []
        # 按帧处理所有音频数据（包括最后一帧可能补零）
        for i in range(0, len(raw_data), frame_size * 2):  # 16bit=2bytes/sample
            # 获取当前帧的二进制数据
            chunk = raw_data[i : i + frame_size * 2]

            # 如果最后一帧不足，补零
            if len(chunk) < frame_size * 2:
                chunk += b"\x00" * (frame_size * 2 - len(chunk))

            if is_opus:
                # 转换为numpy数组处理
                np_frame = np.frombuffer(chunk, dtype=np.int16)
                # 编码Opus数据
                frame_data = encoder.encode(np_frame.tobytes(), frame_size)
            else:
                frame_data = chunk if isinstance(chunk, bytes) else bytes(chunk)

            datas.append(frame_data)

        logger.info(f"[AUDIO UTILS] Converted {audio_file_path} to {len(datas)} packets, duration: {duration:.2f}s")
        return datas, duration
        
    except Exception as e:
        logger.error(f"[AUDIO UTILS] Failed to convert audio file {audio_file_path}: {e}")
        return [], 0.0