import time
import wave
import os
from pathlib import Path
from typing import List
from logger import get_logger


class AudioSaver:
    """音频保存器，将Opus音频数据解码后保存为WAV文件"""
    
    def __init__(self, log_dir="log"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        self.logger = get_logger(self.__class__.__name__)
        
        # 初始化Opus解码器（必须）
        try:
            import opuslib_next
            self.decoder = opuslib_next.Decoder(16000, 1)  # 16kHz, 单声道
            self.logger.info("[AUDIO SAVER] Opus decoder initialized")
        except ImportError:
            self.logger.critical("[AUDIO SAVER] opuslib_next is required but not installed. Please run: pip install opuslib_next")
            raise
        except Exception as e:
            self.logger.critical(f"[AUDIO SAVER] Failed to initialize Opus decoder: {e}")
            raise
        
    def save_audio_as_wav(self, opus_packets: list, session_id: str) -> str:
        """
        保存Opus音频包列表为WAV文件，参考reportHandle.py的实现
        
        Args:
            opus_packets: Opus音频包列表
            session_id: 会话ID，用于生成文件名
            
        Returns:
            str: 保存的WAV文件路径，如果保存失败则返回None
        """
        timestamp = int(time.time() * 1000)
        filename = f"asr_{session_id}_{timestamp}.wav"
        filepath = self.log_dir / filename
        
        try:
            # 参考reportHandle.py的opus_to_wav实现
            pcm_data = []
            
            for opus_packet in opus_packets:
                try:
                    # 每个包解码为960采样点（60ms at 16kHz）
                    pcm_frame = self.decoder.decode(opus_packet, 960)
                    pcm_data.append(pcm_frame)
                except Exception as decode_error:
                    self.logger.debug(f"[AUDIO SAVER] Skip invalid Opus packet: {decode_error}")
                    continue
            
            if not pcm_data:
                self.logger.warning("[AUDIO SAVER] No valid PCM data decoded")
                return None
            
            # 合并所有PCM数据
            pcm_data_bytes = b"".join(pcm_data)
            
            # 使用wave模块保存WAV文件
            with wave.open(str(filepath), 'wb') as wf:
                wf.setnchannels(1)           # 单声道
                wf.setsampwidth(2)            # 16-bit = 2 bytes
                wf.setframerate(16000)        # 16kHz采样率
                wf.writeframes(pcm_data_bytes)    # 写入PCM数据
                
            self.logger.info(f"[AUDIO SAVER] Saved decoded audio to {filepath} ({len(pcm_data_bytes)} PCM bytes from {len(opus_packets)} Opus packets)")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"[AUDIO SAVER] Failed to save audio: {e}")
            return None
    
    def wav_to_opus_packets(self, wav_file_path: str) -> List[bytes]:
        """Convert a WAV file to a list of Opus packets.
        
        Args:
            wav_file_path: Path to the input WAV file
            
        Returns:
            List[bytes]: List of Opus-encoded audio packets
        """
        try:
            if not os.path.exists(wav_file_path):
                self.logger.error(f"[AUDIO SAVER] WAV file not found: {wav_file_path}")
                return []
                
            # Initialize Opus encoder
            import opuslib_next
            encoder = opuslib_next.Encoder(16000, 1, opuslib_next.APPLICATION_VOIP)
            
            # Read WAV file
            with wave.open(wav_file_path, 'rb') as wav_file:
                # Verify WAV file format
                if wav_file.getnchannels() != 1 or wav_file.getsampwidth() != 2 or wav_file.getframerate() != 16000:
                    self.logger.warning(
                        f"[AUDIO SAVER] WAV file format mismatch: {wav_file.getnchannels()}ch, "
                        f"{wav_file.getsampwidth()*8}bit, {wav_file.getframerate()}Hz"
                    )
                
                # Read all PCM data
                pcm_data = wav_file.readframes(wav_file.getnframes())
            
            # Encode PCM data to Opus packets
            opus_packets = []
            frame_size = 960 * 2  # 960 samples * 2 bytes = 1920 bytes per frame (60ms at 16kHz)
            
            for i in range(0, len(pcm_data), frame_size):
                pcm_frame = pcm_data[i:i + frame_size]
                
                # Pad last frame with silence if needed
                if len(pcm_frame) < frame_size:
                    pcm_frame = pcm_frame + b'\x00' * (frame_size - len(pcm_frame))
                
                # Encode to Opus
                opus_packet = encoder.encode(pcm_frame, 960)
                opus_packets.append(opus_packet)
            
            self.logger.info(f"[AUDIO SAVER] Converted WAV to {len(opus_packets)} Opus packets")
            return opus_packets
            
        except Exception as e:
            self.logger.error(f"[AUDIO SAVER] Failed to convert WAV to Opus: {e}")
            return []