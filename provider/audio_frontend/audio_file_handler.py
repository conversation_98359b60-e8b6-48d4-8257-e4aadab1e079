"""
Audio file handling utilities for saving and processing audio files.
"""
import os
import wave
import opuslib_next
import logging
from typing import List, Optional

class AudioFileHandler:
    """Handles audio file operations including saving WAV files and converting to Opus."""
    
    def __init__(self, output_dir: str = "log/audio"):
        """Initialize the audio file handler.
        
        Args:
            output_dir: Directory to save audio files
        """
        self.output_dir = output_dir
        self.logger = logging.getLogger(__name__)
        os.makedirs(self.output_dir, exist_ok=True)
    
    def save_audio_as_wav(self, opus_packets: List[bytes], session_id: str) -> Optional[str]:
        """Save a list of Opus packets as a WAV file.
        
        Args:
            opus_packets: List of Opus-encoded audio packets
            session_id: Unique session ID for the audio file
            
        Returns:
            str: Path to the saved WAV file, or None if saving failed
        """
        if not opus_packets:
            self.logger.warning("No Opus packets to save")
            return None
            
        try:
            # Initialize Opus decoder
            decoder = opuslib_next.Decoder(16000, 1)  # 16kHz, mono
            
            # Create output filename with timestamp
            import time
            timestamp = int(time.time())
            filename = f"asr_{session_id}_{timestamp}.wav"
            filepath = os.path.join(self.output_dir, filename)
            
            # Decode Opus packets to PCM and write to WAV
            with wave.open(filepath, 'wb') as wav_file:
                wav_file.setnchannels(1)  # Mono
                wav_file.setsampwidth(2)   # 16-bit
                wav_file.setframerate(16000)  # 16kHz
                
                for packet in opus_packets:
                    try:
                        # Decode Opus packet to PCM
                        pcm_data = decoder.decode(packet, 960)  # 960 samples per frame (60ms at 16kHz)
                        wav_file.writeframes(pcm_data)
                    except opuslib_next.OpusError as e:
                        self.logger.warning(f"Error decoding Opus packet: {e}")
                        continue
            
            self.logger.info(f"Saved WAV file: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Failed to save WAV file: {e}", exc_info=True)
            return None
    
    def wav_to_opus_packets(self, wav_file_path: str) -> List[bytes]:
        """Convert a WAV file to a list of Opus packets.
        
        Args:
            wav_file_path: Path to the input WAV file
            
        Returns:
            List[bytes]: List of Opus-encoded audio packets
        """
        try:
            if not os.path.exists(wav_file_path):
                self.logger.error(f"WAV file not found: {wav_file_path}")
                return []
                
            # Initialize Opus encoder
            encoder = opuslib_next.Encoder(16000, 1, opuslib_next.APPLICATION_VOIP)
            
            # Read WAV file
            with wave.open(wav_file_path, 'rb') as wav_file:
                # Verify WAV file format
                if wav_file.getnchannels() != 1 or wav_file.getsampwidth() != 2 or wav_file.getframerate() != 16000:
                    self.logger.warning(
                        f"WAV file format mismatch: {wav_file.getnchannels()}ch, "
                        f"{wav_file.getsampwidth()*8}bit, {wav_file.getframerate()}Hz"
                    )
                
                # Read all PCM data
                pcm_data = wav_file.readframes(wav_file.getnframes())
            
            # Encode PCM data to Opus packets
            opus_packets = []
            frame_size = 960 * 2  # 960 samples * 2 bytes = 1920 bytes per frame (60ms at 16kHz)
            
            for i in range(0, len(pcm_data), frame_size):
                pcm_frame = pcm_data[i:i + frame_size]
                
                # Pad last frame with silence if needed
                if len(pcm_frame) < frame_size:
                    pcm_frame = pcm_frame + b'\x00' * (frame_size - len(pcm_frame))
                
                # Encode to Opus
                opus_packet = encoder.encode(pcm_frame, 960)
                opus_packets.append(opus_packet)
            
            self.logger.info(f"Converted WAV to {len(opus_packets)} Opus packets")
            return opus_packets
            
        except Exception as e:
            self.logger.error(f"Failed to convert WAV to Opus: {e}", exc_info=True)
            return []
    
    def cleanup_old_files(self, max_age_days: int = 7) -> None:
        """Clean up old audio files.
        
        Args:
            max_age_days: Maximum age of files to keep in days
        """
        try:
            now = time.time()
            for filename in os.listdir(self.output_dir):
                filepath = os.path.join(self.output_dir, filename)
                if os.path.isfile(filepath) and filename.endswith('.wav'):
                    file_age = now - os.path.getmtime(filepath)
                    if file_age > (max_age_days * 86400):  # Convert days to seconds
                        os.remove(filepath)
                        self.logger.debug(f"Removed old audio file: {filepath}")
        except Exception as e:
            self.logger.error(f"Error cleaning up old audio files: {e}")
