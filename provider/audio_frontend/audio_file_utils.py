import os
import time
import wave
import numpy as np
import opuslib_next
from pathlib import Path
from typing import List, Tuple, Optional
from logger import get_logger

logger = get_logger(__name__)


class AudioFileProcessor:
    """音频文件处理器，提供音频文件转换、保存等功能"""

    def __init__(self, log_dir: str = "log"):
        """初始化音频处理器

        Args:
            log_dir: 音频文件保存目录
        """
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        self.logger = get_logger(self.__class__.__name__)

        # 初始化Opus解码器
        try:
            self.decoder = opuslib_next.Decoder(16000, 1)  # 16kHz, 单声道
            self.logger.info("[AUDIO PROCESSOR] Opus decoder initialized")
        except ImportError:
            self.logger.critical("[AUDIO PROCESSOR] opuslib_next is required but not installed. Please run: pip install opuslib_next")
            raise
        except Exception as e:
            self.logger.critical(f"[AUDIO PROCESSOR] Failed to initialize Opus decoder: {e}")
            raise

    def save_audio_as_wav(self, opus_packets: List[bytes], session_id: str) -> Optional[str]:
        """保存Opus音频包列表为WAV文件

        Args:
            opus_packets: Opus音频包列表
            session_id: 会话ID，用于生成文件名

        Returns:
            str: 保存的WAV文件路径，如果保存失败则返回None
        """
        timestamp = int(time.time() * 1000)
        filename = f"asr_{session_id}_{timestamp}.wav"
        filepath = self.log_dir / filename

        try:
            pcm_data = []

            for opus_packet in opus_packets:
                try:
                    # 每个包解码为960采样点（60ms at 16kHz）
                    pcm_frame = self.decoder.decode(opus_packet, 960)
                    pcm_data.append(pcm_frame)
                except Exception as decode_error:
                    self.logger.debug(f"[AUDIO PROCESSOR] Skip invalid Opus packet: {decode_error}")
                    continue

            if not pcm_data:
                self.logger.warning("[AUDIO PROCESSOR] No valid PCM data decoded")
                return None

            # 合并所有PCM数据
            pcm_data_bytes = b"".join(pcm_data)

            # 使用wave模块保存WAV文件
            with wave.open(str(filepath), 'wb') as wf:
                wf.setnchannels(1)           # 单声道
                wf.setsampwidth(2)            # 16-bit = 2 bytes
                wf.setframerate(16000)        # 16kHz采样率
                wf.writeframes(pcm_data_bytes)    # 写入PCM数据

            self.logger.info(f"[AUDIO PROCESSOR] Saved decoded audio to {filepath} ({len(pcm_data_bytes)} PCM bytes from {len(opus_packets)} Opus packets)")
            return str(filepath)

        except Exception as e:
            self.logger.error(f"[AUDIO PROCESSOR] Failed to save audio: {e}")
            return None

    def wav_to_opus_packets(self, wav_file_path: str) -> List[bytes]:
        """将WAV文件转换为Opus数据包列表

        Args:
            wav_file_path: 输入WAV文件路径

        Returns:
            List[bytes]: Opus编码的音频数据包列表
        """
        try:
            if not os.path.exists(wav_file_path):
                self.logger.error(f"[AUDIO PROCESSOR] WAV file not found: {wav_file_path}")
                return []

            # 初始化Opus编码器
            encoder = opuslib_next.Encoder(16000, 1, opuslib_next.APPLICATION_VOIP)

            # 读取WAV文件
            with wave.open(wav_file_path, 'rb') as wav_file:
                # 验证WAV文件格式
                if wav_file.getnchannels() != 1 or wav_file.getsampwidth() != 2 or wav_file.getframerate() != 16000:
                    self.logger.warning(
                        f"[AUDIO PROCESSOR] WAV file format mismatch: {wav_file.getnchannels()}ch, "
                        f"{wav_file.getsampwidth()*8}bit, {wav_file.getframerate()}Hz"
                    )

                # 读取所有PCM数据
                pcm_data = wav_file.readframes(wav_file.getnframes())

            # 将PCM数据编码为Opus数据包
            opus_packets = []
            frame_size = 960 * 2  # 960 samples * 2 bytes = 1920 bytes per frame (60ms at 16kHz)

            for i in range(0, len(pcm_data), frame_size):
                pcm_frame = pcm_data[i:i + frame_size]

                # 如果最后一帧不足，用静音补齐
                if len(pcm_frame) < frame_size:
                    pcm_frame = pcm_frame + b'\x00' * (frame_size - len(pcm_frame))

                # 编码为Opus
                opus_packet = encoder.encode(pcm_frame, 960)
                opus_packets.append(opus_packet)

            self.logger.info(f"[AUDIO PROCESSOR] Converted WAV to {len(opus_packets)} Opus packets")
            return opus_packets

        except Exception as e:
            self.logger.error(f"[AUDIO PROCESSOR] Failed to convert WAV to Opus: {e}")
            return []


def audio_to_data(audio_file_path: str, is_opus: bool = True) -> Tuple[List[bytes], float]:
    """将音频文件转换为Opus数据包列表，参考yuyan-server的实现

    Args:
        audio_file_path: 音频文件路径
        is_opus: 是否输出为Opus格式

    Returns:
        Tuple[List[bytes], float]: (音频数据包列表, 音频时长秒数)
    """
    try:
        # 导入pydub用于音频处理
        from pydub import AudioSegment
    except ImportError:
        logger.error("[AUDIO PROCESSOR] pydub is required but not installed. Please run: pip install pydub")
        raise

    try:
        # 获取文件后缀名
        file_type = os.path.splitext(audio_file_path)[1]
        if file_type:
            file_type = file_type.lstrip(".")

        # 读取音频文件，-nostdin 参数：不要从标准输入读取数据，否则FFmpeg会阻塞
        audio = AudioSegment.from_file(
            audio_file_path, format=file_type, parameters=["-nostdin"]
        )

        # 转换为单声道/16kHz采样率/16位小端编码（确保与编码器匹配）
        audio = audio.set_channels(1).set_frame_rate(16000).set_sample_width(2)

        # 音频时长(秒)
        duration = len(audio) / 1000.0

        # 获取原始PCM数据（16位小端）
        raw_data = audio.raw_data

        # 初始化Opus编码器
        encoder = opuslib_next.Encoder(16000, 1, opuslib_next.APPLICATION_AUDIO)

        # 编码参数
        frame_duration = 60  # 60ms per frame
        frame_size = int(16000 * frame_duration / 1000)  # 960 samples/frame

        datas = []
        # 按帧处理所有音频数据（包括最后一帧可能补零）
        for i in range(0, len(raw_data), frame_size * 2):  # 16bit=2bytes/sample
            # 获取当前帧的二进制数据
            chunk = raw_data[i : i + frame_size * 2]

            # 如果最后一帧不足，补零
            if len(chunk) < frame_size * 2:
                chunk += b"\x00" * (frame_size * 2 - len(chunk))

            if is_opus:
                # 转换为numpy数组处理
                np_frame = np.frombuffer(chunk, dtype=np.int16)
                # 编码Opus数据
                frame_data = encoder.encode(np_frame.tobytes(), frame_size)
            else:
                frame_data = chunk if isinstance(chunk, bytes) else bytes(chunk)

            datas.append(frame_data)

        logger.info(f"[AUDIO PROCESSOR] Converted {audio_file_path} to {len(datas)} packets, duration: {duration:.2f}s")
        return datas, duration

    except Exception as e:
        logger.error(f"[AUDIO PROCESSOR] Failed to convert audio file {audio_file_path}: {e}")
        return [], 0.0


