from typing import Dict, Any, List, Optional, Annotated, TypedDict
from datetime import datetime, timedelta
import asyncio
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langgraph.types import Command
from ..audio_frontend.vad import SileroVAD


class VadState(TypedDict):
    """State schema for VadAgent"""
    messages: Annotated[List, add_messages]
    session_id: str
    is_monitoring: bool
    fast_reply_speaking: bool
    interruption_detected: bool
    audio_activity_level: float
    last_speech_time: Optional[datetime]
    consecutive_speech_frames: int
    interruption_threshold: int


class VadAgent:
    """LangGraph-based agent for Voice Activity Detection and interruption handling"""
    
    def __init__(self, config: Dict[str, Any], event_callback=None):
        self.config = config
        self.event_callback = event_callback  # Callback to send interruption signals
        self.logger = None
        self.vad = SileroVAD(config)
        self.graph = None
        
        # VAD configuration
        self.interruption_threshold = 3  # Number of consecutive speech frames to trigger interruption
        self.speech_confidence_threshold = 0.7  # Confidence threshold for speech detection
        
    def set_logger(self, logger):
        self.logger = logger
        
    async def initialize(self) -> bool:
        """Initialize the VadAgent with LangGraph"""
        try:
            # Build LangGraph
            self.graph = self._build_graph()
            
            if self.logger:
                self.logger.info("VadAgent initialized with LangGraph")
            return True
        except Exception as e:
            if self.logger:
                self.logger.error(f"Failed to initialize VadAgent: {e}")
            return False
    
    def _build_graph(self) -> StateGraph:
        """Build LangGraph workflow for VAD and interruption detection"""
        workflow = StateGraph(VadState)
        
        # Add nodes
        workflow.add_node("check_monitoring_status", self._check_monitoring_status_node)
        workflow.add_node("analyze_audio_activity", self._analyze_audio_activity_node)
        workflow.add_node("evaluate_interruption", self._evaluate_interruption_node)
        workflow.add_node("send_interruption_signal", self._send_interruption_signal_node)
        workflow.add_node("update_monitoring_state", self._update_monitoring_state_node)
        
        # Define flow
        workflow.add_edge(START, "check_monitoring_status")
        workflow.add_conditional_edges(
            "check_monitoring_status",
            self._route_after_status_check,
            {
                "monitor": "analyze_audio_activity",
                "skip": END
            }
        )
        workflow.add_edge("analyze_audio_activity", "evaluate_interruption")
        workflow.add_conditional_edges(
            "evaluate_interruption",
            self._route_after_interruption_eval,
            {
                "interrupt": "send_interruption_signal",
                "continue": "update_monitoring_state"
            }
        )
        workflow.add_edge("send_interruption_signal", "update_monitoring_state")
        workflow.add_edge("update_monitoring_state", END)
        
        return workflow.compile()
    
    async def _check_monitoring_status_node(self, state: VadState) -> VadState:
        """Check if we should monitor for interruptions"""
        if self.logger:
            self.logger.debug("VadAgent checking monitoring status")
        
        # Only monitor if FastReplyAgent is speaking
        should_monitor = state.get("fast_reply_speaking", False)
        
        return {
            **state,
            "is_monitoring": should_monitor
        }
    
    def _route_after_status_check(self, state: VadState) -> str:
        """Route based on monitoring status"""
        if state.get("is_monitoring", False):
            return "monitor"
        else:
            return "skip"
    
    async def _analyze_audio_activity_node(self, state: VadState) -> VadState:
        """Analyze audio activity for potential interruption"""
        if self.logger:
            self.logger.debug("VadAgent analyzing audio activity")
        
        # This would typically receive real-time audio data
        # For now, we simulate audio activity analysis
        
        # In a real implementation, this would:
        # 1. Process incoming audio frames
        # 2. Use VAD to detect speech
        # 3. Calculate speech confidence and activity level
        
        # Simulated values - in real implementation, these would come from actual audio processing
        audio_activity_level = 0.0  # Will be updated with real audio processing
        current_time = datetime.now()
        
        return {
            **state,
            "audio_activity_level": audio_activity_level,
            "last_speech_time": current_time
        }
    
    async def _evaluate_interruption_node(self, state: VadState) -> VadState:
        """Evaluate if interruption should be triggered"""
        if self.logger:
            self.logger.debug("VadAgent evaluating interruption conditions")
        
        audio_activity = state.get("audio_activity_level", 0.0)
        consecutive_frames = state.get("consecutive_speech_frames", 0)
        threshold = state.get("interruption_threshold", self.interruption_threshold)
        
        # Check if we have sufficient speech activity to consider interruption
        if audio_activity > self.speech_confidence_threshold:
            consecutive_frames += 1
        else:
            consecutive_frames = 0
        
        # Determine if interruption should be triggered
        should_interrupt = consecutive_frames >= threshold
        
        return {
            **state,
            "consecutive_speech_frames": consecutive_frames,
            "interruption_detected": should_interrupt
        }
    
    def _route_after_interruption_eval(self, state: VadState) -> str:
        """Route based on interruption evaluation"""
        if state.get("interruption_detected", False):
            return "interrupt"
        else:
            return "continue"
    
    async def _send_interruption_signal_node(self, state: VadState) -> VadState:
        """Send interruption signal to other agents"""
        if self.logger:
            self.logger.info("VadAgent sending interruption signal")
        
        try:
            session_id = state.get("session_id", "")
            
            # Send interruption signal through callback
            if self.event_callback:
                await self.event_callback({
                    "type": "interrupt",
                    "session_id": session_id,
                    "data": {
                        "reason": "user_interruption",
                        "confidence": state.get("audio_activity_level", 0.0),
                        "timestamp": datetime.now().isoformat()
                    }
                })
            
            # Add interruption message to state
            interrupt_message = AIMessage(content="检测到用户打断")
            
            return {
                **state,
                "messages": [interrupt_message]
            }
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error sending interruption signal: {e}")
            return state
    
    async def _update_monitoring_state_node(self, state: VadState) -> VadState:
        """Update monitoring state for next iteration"""
        if self.logger:
            self.logger.debug("VadAgent updating monitoring state")
        
        # Reset consecutive frames if interruption was triggered
        if state.get("interruption_detected", False):
            consecutive_frames = 0
        else:
            consecutive_frames = state.get("consecutive_speech_frames", 0)
        
        return {
            **state,
            "consecutive_speech_frames": consecutive_frames,
            "interruption_detected": False  # Reset for next cycle
        }
    
    async def process_audio_frame(self, audio_data: bytes, session_id: str, fast_reply_speaking: bool = False) -> Dict[str, Any]:
        """Process a single audio frame for VAD analysis"""
        try:
            # Use VAD to analyze the audio frame
            vad_result = self.vad.process_audio_frame(audio_data)
            
            # Extract speech detection information
            speech_detected = vad_result.get('speech_detected', False)
            speech_confidence = vad_result.get('speech_probability', 0.0)
            
            # Create state for this frame
            initial_state = VadState(
                messages=[],
                session_id=session_id,
                is_monitoring=False,
                fast_reply_speaking=fast_reply_speaking,
                interruption_detected=False,
                audio_activity_level=speech_confidence if speech_detected else 0.0,
                last_speech_time=datetime.now() if speech_detected else None,
                consecutive_speech_frames=0,
                interruption_threshold=self.interruption_threshold
            )
            
            # Process through LangGraph workflow
            final_state = await self.graph.ainvoke(initial_state)
            
            return {
                "interruption_detected": final_state.get("interruption_detected", False),
                "speech_activity": speech_confidence,
                "vad_result": vad_result
            }
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error processing audio frame: {e}")
            return {
                "interruption_detected": False,
                "speech_activity": 0.0,
                "vad_result": {}
            }
    
    async def set_fast_reply_status(self, session_id: str, is_speaking: bool):
        """Update FastReplyAgent speaking status"""
        if self.logger:
            self.logger.debug(f"VadAgent updating FastReply status: speaking={is_speaking}")
        
        # This would typically update the agent's internal state
        # to track when FastReplyAgent is speaking
        
        # In a more sophisticated implementation, this could trigger
        # a state update in the LangGraph workflow
        pass
    
    async def handle_agent_status_event(self, event_data: Dict[str, Any]):
        """Handle agent status events from other agents"""
        try:
            status = event_data.get("status")
            session_id = event_data.get("session_id", "")
            
            if status == "speaking":
                await self.set_fast_reply_status(session_id, True)
            elif status == "stopped":
                await self.set_fast_reply_status(session_id, False)
            elif status == "paused":
                await self.set_fast_reply_status(session_id, False)
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error handling agent status event: {e}")
    
    def configure_interruption_sensitivity(self, threshold: int, confidence: float):
        """Configure interruption detection sensitivity"""
        self.interruption_threshold = max(1, min(10, threshold))  # Clamp between 1-10
        self.speech_confidence_threshold = max(0.1, min(1.0, confidence))  # Clamp between 0.1-1.0
        
        if self.logger:
            self.logger.info(f"VadAgent configured: threshold={self.interruption_threshold}, confidence={self.speech_confidence_threshold}")
    
    async def process_realtime_audio(self, audio_stream, session_id: str, fast_reply_speaking_callback=None):
        """Process real-time audio stream for continuous VAD monitoring"""
        if self.logger:
            self.logger.info("VadAgent starting real-time audio processing")
        
        try:
            async for audio_chunk in audio_stream:
                # Check FastReplyAgent speaking status
                is_speaking = False
                if fast_reply_speaking_callback:
                    is_speaking = await fast_reply_speaking_callback()
                
                # Process the audio chunk
                result = await self.process_audio_frame(audio_chunk, session_id, is_speaking)
                
                # If interruption detected, log it
                if result.get("interruption_detected", False):
                    if self.logger:
                        self.logger.info(f"Interruption detected for session {session_id}")
                
                # Small delay to prevent overwhelming the system
                await asyncio.sleep(0.01)
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error in real-time audio processing: {e}")
    
    def get_vad_statistics(self) -> Dict[str, Any]:
        """Get VAD processing statistics"""
        return {
            "interruption_threshold": self.interruption_threshold,
            "speech_confidence_threshold": self.speech_confidence_threshold,
            "vad_model_info": {
                "model_name": "SileroVAD",
                "threshold": self.vad.config.get('vad', {}).get('threshold', 0.5)
            }
        }