from typing import Dict, Any, List, Optional, Annotated, TypedDict
from datetime import datetime
import asyncio
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.runnables import Runnable
from langgraph.types import Command
from ..llm import llm_factory


class FastReplyState(TypedDict):
    """State schema for FastReplyAgent"""
    messages: Annotated[List, add_messages]
    audio_file: Optional[str]
    session_id: str
    is_speaking: bool
    should_interrupt: bool
    search_result: Optional[str]
    response_ready: bool


class FastReplyAgent:
    """LangGraph-based agent for quick multimodal responses"""
    
    def __init__(self, config: Dict[str, Any], connection_handler):
        self.config = config
        self.connection_handler = connection_handler
        self.logger = None
        self.llm_provider = None
        self.graph = None
        
    def set_logger(self, logger):
        self.logger = logger
        
    async def initialize(self) -> bool:
        """Initialize the FastReplyAgent with <PERSON><PERSON><PERSON>h"""
        try:
            # Initialize LLM provider
            self.llm_provider = llm_factory.create_audio_llm_provider(self.config)
            
            # Build LangGraph
            self.graph = self._build_graph()
            
            if self.logger:
                self.logger.info("FastReplyAgent initialized with LangGraph")
            return True
        except Exception as e:
            if self.logger:
                self.logger.error(f"Failed to initialize FastReplyAgent: {e}")
            return False
    
    def _build_graph(self) -> StateGraph:
        """Build LangGraph workflow for fast reply"""
        workflow = StateGraph(FastReplyState)
        
        # Add nodes
        workflow.add_node("analyze_input", self._analyze_input_node)
        workflow.add_node("generate_quick_response", self._generate_quick_response_node)
        workflow.add_node("check_interruption", self._check_interruption_node)
        workflow.add_node("send_response", self._send_response_node)
        workflow.add_node("wait_for_search", self._wait_for_search_node)
        workflow.add_node("enhance_response", self._enhance_response_node)
        
        # Define flow
        workflow.add_edge(START, "analyze_input")
        workflow.add_conditional_edges(
            "analyze_input",
            self._route_after_analysis,
            {
                "quick_response": "generate_quick_response",
                "wait_search": "wait_for_search"
            }
        )
        workflow.add_edge("generate_quick_response", "check_interruption")
        workflow.add_conditional_edges(
            "check_interruption",
            self._route_after_interruption_check,
            {
                "send": "send_response",
                "stop": END
            }
        )
        workflow.add_edge("send_response", END)
        workflow.add_edge("wait_for_search", "enhance_response")
        workflow.add_edge("enhance_response", "check_interruption")
        
        return workflow.compile()
    
    async def _analyze_input_node(self, state: FastReplyState) -> FastReplyState:
        """Analyze input and determine response strategy"""
        if self.logger:
            self.logger.info("FastReplyAgent analyzing input")
        
        # Quick analysis to determine if we should wait for search results
        should_wait_for_search = False
        
        if state.get("audio_file"):
            # For audio, always provide quick response first
            should_wait_for_search = False
        elif state.get("messages"):
            # For text, check if it's a complex query
            last_message = state["messages"][-1]
            if isinstance(last_message, HumanMessage):
                text = last_message.content
                # Simple heuristic: if question contains question words, might benefit from search
                question_words = ["什么", "为什么", "怎么", "如何", "哪里", "什么时候"]
                should_wait_for_search = any(word in text for word in question_words)
        
        return {
            **state,
            "response_ready": not should_wait_for_search
        }
    
    def _route_after_analysis(self, state: FastReplyState) -> str:
        """Route based on analysis results"""
        if state.get("response_ready", True):
            return "quick_response"
        else:
            return "wait_search"
    
    async def _generate_quick_response_node(self, state: FastReplyState) -> FastReplyState:
        """Generate quick response using multimodal LLM"""
        if self.logger:
            self.logger.info("FastReplyAgent generating quick response")
        
        try:
            if state.get("audio_file"):
                # Audio input processing
                response_text = await self._quick_audio_analysis(state["audio_file"])
            else:
                # Text input processing
                last_message = state["messages"][-1] if state.get("messages") else None
                if last_message and isinstance(last_message, HumanMessage):
                    response_text = f"收到你的消息：{last_message.content}"
                else:
                    response_text = "我听到了你的消息"
            
            # Add AI response to messages
            ai_message = AIMessage(content=response_text)
            
            return {
                **state,
                "messages": [ai_message],
                "is_speaking": True,
                "should_interrupt": False
            }
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error generating quick response: {e}")
            
            error_message = AIMessage(content="抱歉，我现在无法回应")
            return {
                **state,
                "messages": [error_message],
                "is_speaking": True
            }
    
    async def _check_interruption_node(self, state: FastReplyState) -> FastReplyState:
        """Check if response should be interrupted"""
        # This would be updated by VadAgent through state management
        should_interrupt = state.get("should_interrupt", False)
        
        if should_interrupt:
            if self.logger:
                self.logger.info("FastReplyAgent response interrupted")
        
        return {
            **state,
            "should_interrupt": should_interrupt
        }
    
    def _route_after_interruption_check(self, state: FastReplyState) -> str:
        """Route based on interruption status"""
        if state.get("should_interrupt", False):
            return "stop"
        else:
            return "send"
    
    async def _send_response_node(self, state: FastReplyState) -> FastReplyState:
        """Send response to client"""
        if self.logger:
            self.logger.info("FastReplyAgent sending response")
        
        try:
            # Get the AI response
            ai_messages = [msg for msg in state.get("messages", []) if isinstance(msg, AIMessage)]
            if ai_messages:
                response_text = ai_messages[-1].content
                await self.connection_handler._send_text_tts_sequence(response_text)
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error sending response: {e}")
        
        return {
            **state,
            "is_speaking": False
        }
    
    async def _wait_for_search_node(self, state: FastReplyState) -> FastReplyState:
        """Wait for search results from SearchAgent"""
        if self.logger:
            self.logger.info("FastReplyAgent waiting for search results")
        
        # This is a placeholder - in real implementation, this would
        # wait for SearchAgent to provide enhanced information
        # For now, we'll generate a basic response and indicate we're waiting
        
        basic_response = "让我为你查找更详细的信息..."
        ai_message = AIMessage(content=basic_response)
        
        return {
            **state,
            "messages": [ai_message],
            "response_ready": True
        }
    
    async def _enhance_response_node(self, state: FastReplyState) -> FastReplyState:
        """Enhance response with search results"""
        if self.logger:
            self.logger.info("FastReplyAgent enhancing response with search results")
        
        search_result = state.get("search_result", "")
        if search_result:
            enhanced_response = f"根据搜索到的信息：{search_result}"
        else:
            enhanced_response = "抱歉，暂时无法获取更多信息"
        
        ai_message = AIMessage(content=enhanced_response)
        
        return {
            **state,
            "messages": [ai_message],
            "is_speaking": True
        }
    
    async def _quick_audio_analysis(self, audio_file_path: str) -> str:
        """Quick audio analysis using multimodal LLM"""
        try:
            system_prompt = """你是一个快速响应的AI助手。请简短、自然地回复用户说的话。
保持对话流畅，回复要快速、准确、有帮助。"""
            
            user_context = "请快速回复这段语音"
            
            response_text = await self.llm_provider.generate(
                system_prompt=system_prompt,
                user_context=user_context,
                file_path=audio_file_path
            )
            
            return response_text.strip() if response_text else "好的，我听到了"
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Quick audio analysis error: {e}")
            return "抱歉，我没有听清楚"
    
    async def process_audio_event(self, audio_file: str, session_id: str) -> Dict[str, Any]:
        """Process audio input through LangGraph workflow"""
        initial_state = FastReplyState(
            messages=[],
            audio_file=audio_file,
            session_id=session_id,
            is_speaking=False,
            should_interrupt=False,
            search_result=None,
            response_ready=False
        )
        
        try:
            final_state = await self.graph.ainvoke(initial_state)
            return final_state
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error in FastReplyAgent workflow: {e}")
            return initial_state
    
    async def process_text_event(self, text: str, session_id: str) -> Dict[str, Any]:
        """Process text input through LangGraph workflow"""
        human_message = HumanMessage(content=text)
        
        initial_state = FastReplyState(
            messages=[human_message],
            audio_file=None,
            session_id=session_id,
            is_speaking=False,
            should_interrupt=False,
            search_result=None,
            response_ready=False
        )
        
        try:
            final_state = await self.graph.ainvoke(initial_state)
            return final_state
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error in FastReplyAgent workflow: {e}")
            return initial_state
    
    async def handle_interrupt(self, session_id: str):
        """Handle interruption signal"""
        if self.logger:
            self.logger.info(f"FastReplyAgent received interrupt for session {session_id}")
        # In a real implementation, this would update the agent's state
        # to stop current processing
    
    async def update_with_search_result(self, search_result: str, session_id: str):
        """Update agent with search results from SearchAgent"""
        if self.logger:
            self.logger.info(f"FastReplyAgent received search result for session {session_id}")
        
        # This would typically update the agent's state and potentially
        # trigger additional response generation
        enhanced_response = f"补充信息：{search_result}"
        try:
            await self.connection_handler._send_text_tts_sequence(enhanced_response)
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error sending enhanced response: {e}")