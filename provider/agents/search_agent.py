from typing import Dict, Any, List, Optional, Annotated, TypedDict
from datetime import datetime
import asyncio
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langgraph.types import Command


class SearchState(TypedDict):
    """State schema for SearchAgent"""
    messages: Annotated[List, add_messages]
    audio_file: Optional[str]
    session_id: str
    analysis_complete: bool
    search_query: Optional[str]
    search_results: Optional[str]
    final_result: Optional[str]
    thinking_process: Optional[str]


class SearchAgent:
    """LangGraph-based agent for deep analysis and search using Gemini 2.5 Pro Thinking"""
    
    def __init__(self, config: Dict[str, Any], event_callback=None):
        self.config = config
        self.event_callback = event_callback  # Callback to send results to other agents
        self.logger = None
        self.gemini_provider = None
        self.graph = None
        
    def set_logger(self, logger):
        self.logger = logger
        
    async def initialize(self) -> bool:
        """Initialize the SearchAgent with LangGraph and Gemini 2.5 Pro"""
        try:
            # Initialize Gemini 2.5 Pro Thinking
            from ..llm.gemini_provider import GeminiProvider
            gemini_config = self.config.get('gemini', {})
            gemini_config['model'] = 'gemini-2.5-pro-thinking'
            self.gemini_provider = GeminiProvider(gemini_config)
            
            # Build LangGraph
            self.graph = self._build_graph()
            
            if self.logger:
                self.logger.info("SearchAgent initialized with LangGraph and Gemini 2.5 Pro Thinking")
            return True
        except Exception as e:
            if self.logger:
                self.logger.error(f"Failed to initialize SearchAgent: {e}")
            return False
    
    def _build_graph(self) -> StateGraph:
        """Build LangGraph workflow for search and analysis"""
        workflow = StateGraph(SearchState)
        
        # Add nodes
        workflow.add_node("analyze_content", self._analyze_content_node)
        workflow.add_node("extract_search_query", self._extract_search_query_node)
        workflow.add_node("thinking_analysis", self._thinking_analysis_node)
        workflow.add_node("web_search", self._web_search_node)
        workflow.add_node("synthesize_results", self._synthesize_results_node)
        workflow.add_node("send_to_fast_reply", self._send_to_fast_reply_node)
        
        # Define flow
        workflow.add_edge(START, "analyze_content")
        workflow.add_edge("analyze_content", "extract_search_query")
        workflow.add_conditional_edges(
            "extract_search_query",
            self._route_after_query_extraction,
            {
                "search": "web_search",
                "thinking": "thinking_analysis"
            }
        )
        workflow.add_edge("web_search", "synthesize_results")
        workflow.add_edge("thinking_analysis", "synthesize_results")
        workflow.add_edge("synthesize_results", "send_to_fast_reply")
        workflow.add_edge("send_to_fast_reply", END)
        
        return workflow.compile()
    
    async def _analyze_content_node(self, state: SearchState) -> SearchState:
        """Analyze input content to understand user intent"""
        if self.logger:
            self.logger.info("SearchAgent analyzing content")
        
        try:
            content = ""
            if state.get("audio_file"):
                # For audio, we'll analyze the file
                content = f"Audio file: {state['audio_file']}"
            elif state.get("messages"):
                # For text messages, get the content
                human_messages = [msg for msg in state["messages"] if isinstance(msg, HumanMessage)]
                if human_messages:
                    content = human_messages[-1].content
            
            analysis_message = AIMessage(content=f"分析内容：{content}")
            
            return {
                **state,
                "messages": [analysis_message]
            }
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error in content analysis: {e}")
            return state
    
    async def _extract_search_query_node(self, state: SearchState) -> SearchState:
        """Extract search query from analyzed content"""
        if self.logger:
            self.logger.info("SearchAgent extracting search query")
        
        try:
            # Use Gemini to extract search queries
            if state.get("audio_file"):
                query = await self._extract_query_from_audio(state["audio_file"])
            else:
                # Extract from text messages
                human_messages = [msg for msg in state["messages"] if isinstance(msg, HumanMessage)]
                if human_messages:
                    query = await self._extract_query_from_text(human_messages[-1].content)
                else:
                    query = None
            
            return {
                **state,
                "search_query": query
            }
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error extracting search query: {e}")
            return {**state, "search_query": None}
    
    def _route_after_query_extraction(self, state: SearchState) -> str:
        """Route based on whether we need web search or just thinking"""
        search_query = state.get("search_query")
        
        # If we have a specific search query, do web search
        # Otherwise, use thinking mode for analysis
        if search_query and len(search_query) > 5:
            return "search"
        else:
            return "thinking"
    
    async def _web_search_node(self, state: SearchState) -> SearchState:
        """Perform web search using search query"""
        if self.logger:
            self.logger.info("SearchAgent performing web search")
        
        search_query = state.get("search_query", "")
        
        try:
            # Placeholder for actual web search implementation
            # In a real implementation, this would use search APIs
            search_results = f"搜索结果for '{search_query}': [模拟搜索结果]"
            
            return {
                **state,
                "search_results": search_results
            }
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error in web search: {e}")
            return {**state, "search_results": None}
    
    async def _thinking_analysis_node(self, state: SearchState) -> SearchState:
        """Perform deep thinking analysis using Gemini 2.5 Pro Thinking"""
        if self.logger:
            self.logger.info("SearchAgent performing thinking analysis")
        
        try:
            if state.get("audio_file"):
                thinking_result = await self._deep_audio_thinking(state["audio_file"])
            else:
                # Thinking analysis for text
                human_messages = [msg for msg in state["messages"] if isinstance(msg, HumanMessage)]
                if human_messages:
                    thinking_result = await self._deep_text_thinking(human_messages[-1].content)
                else:
                    thinking_result = "无法进行深度分析"
            
            return {
                **state,
                "thinking_process": thinking_result
            }
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error in thinking analysis: {e}")
            return {**state, "thinking_process": None}
    
    async def _synthesize_results_node(self, state: SearchState) -> SearchState:
        """Synthesize search results and thinking process"""
        if self.logger:
            self.logger.info("SearchAgent synthesizing results")
        
        try:
            search_results = state.get("search_results", "")
            thinking_process = state.get("thinking_process", "")
            
            # Combine results
            if search_results and thinking_process:
                final_result = f"综合分析结果：\n搜索信息：{search_results}\n深度思考：{thinking_process}"
            elif search_results:
                final_result = f"搜索结果：{search_results}"
            elif thinking_process:
                final_result = f"分析结果：{thinking_process}"
            else:
                final_result = "抱歉，无法获得有效的分析结果"
            
            return {
                **state,
                "final_result": final_result,
                "analysis_complete": True
            }
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error synthesizing results: {e}")
            return {**state, "final_result": "分析过程中出现错误"}
    
    async def _send_to_fast_reply_node(self, state: SearchState) -> SearchState:
        """Send results to FastReplyAgent"""
        if self.logger:
            self.logger.info("SearchAgent sending results to FastReplyAgent")
        
        try:
            final_result = state.get("final_result", "")
            session_id = state.get("session_id", "")
            
            # Send results through callback
            if self.event_callback and final_result:
                await self.event_callback({
                    "type": "search_result",
                    "session_id": session_id,
                    "data": {"result": final_result}
                })
            
            return state
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error sending results: {e}")
            return state
    
    async def _extract_query_from_audio(self, audio_file_path: str) -> Optional[str]:
        """Extract search query from audio file"""
        try:
            system_prompt = """分析这段语音，提取出用户可能需要搜索的关键信息。
如果用户在询问具体问题、寻求信息或需要查找资料，请提取搜索关键词。
如果只是日常对话，返回空字符串。

格式：只返回搜索关键词，不要额外解释。"""
            
            user_context = "请提取搜索关键词"
            
            response = await asyncio.to_thread(
                self._gemini_sync_call,
                system_prompt,
                user_context,
                audio_file_path
            )
            
            return response.strip() if response and len(response.strip()) > 2 else None
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error extracting query from audio: {e}")
            return None
    
    async def _extract_query_from_text(self, text: str) -> Optional[str]:
        """Extract search query from text"""
        try:
            system_prompt = """分析这段文本，提取出用户可能需要搜索的关键信息。
如果用户在询问具体问题、寻求信息或需要查找资料，请提取搜索关键词。
如果只是日常对话，返回空字符串。

格式：只返回搜索关键词，不要额外解释。"""
            
            user_context = f"用户说：{text}"
            
            response = await asyncio.to_thread(
                self._gemini_sync_text_call,
                system_prompt,
                user_context
            )
            
            return response.strip() if response and len(response.strip()) > 2 else None
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error extracting query from text: {e}")
            return None
    
    async def _deep_audio_thinking(self, audio_file_path: str) -> str:
        """Deep thinking analysis of audio using Gemini 2.5 Pro Thinking"""
        try:
            system_prompt = """你是一个专业的AI分析师，具有深度思考能力。
请仔细分析这段语音内容，并进行深入思考：

1. 理解用户的真实意图和需求
2. 分析语音中的情感、语调和隐含信息
3. 思考用户可能需要什么样的帮助
4. 提供深入、有见解的分析

请使用你的thinking能力进行全面分析。"""
            
            user_context = "请深度分析这段语音"
            
            response = await asyncio.to_thread(
                self._gemini_sync_call,
                system_prompt,
                user_context,
                audio_file_path
            )
            
            return response.strip() if response else "无法进行深度分析"
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error in deep audio thinking: {e}")
            return "深度分析过程中出现错误"
    
    async def _deep_text_thinking(self, text: str) -> str:
        """Deep thinking analysis of text using Gemini 2.5 Pro Thinking"""
        try:
            system_prompt = """你是一个专业的AI分析师，具有深度思考能力。
请仔细分析这段文本内容，并进行深入思考：

1. 理解用户的真实意图和需求
2. 分析文本中的深层含义和隐含信息
3. 思考用户可能需要什么样的帮助
4. 提供深入、有见解的分析

请使用你的thinking能力进行全面分析。"""
            
            user_context = f"用户说：{text}"
            
            response = await asyncio.to_thread(
                self._gemini_sync_text_call,
                system_prompt,
                user_context
            )
            
            return response.strip() if response else "无法进行深度分析"
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error in deep text thinking: {e}")
            return "深度分析过程中出现错误"
    
    def _gemini_sync_call(self, system_prompt: str, user_context: str, audio_file_path: str) -> str:
        """Synchronous Gemini call for audio analysis"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                response = loop.run_until_complete(
                    self.gemini_provider.generate(
                        system_prompt=system_prompt,
                        user_context=user_context,
                        file_path=audio_file_path
                    )
                )
                return response
            finally:
                loop.close()
        except Exception as e:
            if self.logger:
                self.logger.error(f"Gemini sync call error: {e}")
            return ""
    
    def _gemini_sync_text_call(self, system_prompt: str, user_context: str) -> str:
        """Synchronous Gemini call for text analysis"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                response = loop.run_until_complete(
                    self.gemini_provider.generate_text(
                        system_prompt=system_prompt,
                        user_context=user_context
                    )
                )
                return response
            finally:
                loop.close()
        except Exception as e:
            if self.logger:
                self.logger.error(f"Gemini sync text call error: {e}")
            return ""
    
    async def process_audio_event(self, audio_file: str, session_id: str) -> Dict[str, Any]:
        """Process audio input through LangGraph workflow"""
        initial_state = SearchState(
            messages=[],
            audio_file=audio_file,
            session_id=session_id,
            analysis_complete=False,
            search_query=None,
            search_results=None,
            final_result=None,
            thinking_process=None
        )
        
        try:
            final_state = await self.graph.ainvoke(initial_state)
            return final_state
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error in SearchAgent workflow: {e}")
            return initial_state
    
    async def process_text_event(self, text: str, session_id: str) -> Dict[str, Any]:
        """Process text input through LangGraph workflow"""
        human_message = HumanMessage(content=text)
        
        initial_state = SearchState(
            messages=[human_message],
            audio_file=None,
            session_id=session_id,
            analysis_complete=False,
            search_query=None,
            search_results=None,
            final_result=None,
            thinking_process=None
        )
        
        try:
            final_state = await self.graph.ainvoke(initial_state)
            return final_state
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error in SearchAgent workflow: {e}")
            return initial_state