from typing import Dict, Any
from .base_service import BaseService
from .audio_frontend.vad import SileroVAD
from .audio_frontend.audio_file_utils import AudioFileProcessor
from .llm.openai_provider import OpenAIProvider
from .llm.gemini_provider import GeminiProvider

class EchoService(BaseService):
    """Echo service that returns user's audio back after they finish speaking."""
    
    def __init__(self, config: Dict[str, Any], connection_handler=None):
        super().__init__(config, connection_handler)
        self.audio_buffer = bytearray()
        self.vad = SileroVAD(config)
        # Initialize audio saver with output directory from config or use default
        audio_config = config.get('audio', {})
        output_dir = audio_config.get('output_dir', 'log/audio')
        self.audio_saver = AudioFileProcessor(output_dir)
        # Initialize LLM provider based on config
        self.llm_provider = None
        service_type = config.get('service', {}).get('type', 'echo')
        
        # Try to initialize LLM provider (prefer OpenAI, fallback to Gemini)
        try:
            if 'openai' in config and config['openai'].get('api_key'):
                self.llm_provider = OpenAIProvider(config)
                self.logger.info("Using OpenAI LLM provider")
            elif 'gemini' in config and config['gemini'].get('api_key'):
                self.llm_provider = GeminiProvider(config)
                self.logger.info("Using Gemini LLM provider")
            else:
                self.logger.warning("No LLM provider available - missing API keys")
        except Exception as e:
            self.logger.warning(f"LLM provider initialization failed: {e}")
            self.llm_provider = None
        
    async def initialize(self) -> bool:
        """Initialize the echo service"""
        self.logger.info("Initialized")
        return True
        
    async def handle_audio_input(self, audio_data: bytes) -> None:
        """Process audio data with VAD detection."""
        # 使用VAD检测语音活动
        vad_result = self.vad.process_audio_frame(audio_data)
        
        if vad_result['speech_end']:
            # 检测到语音结束，保存音频文件并回声
            opus_packets = vad_result['audio_buffer']
            if opus_packets and len(opus_packets) > 10:  # 确保有足够的音频包
                self.logger.info(f"Speech ended, processing {len(opus_packets)} Opus packets")
                
                # Save as WAV file
                wav_file = self.audio_saver.save_audio_as_wav(opus_packets, self.session_id)
                if wav_file:
                    self.logger.info(f"Audio saved to: {wav_file}")
                    
                    # Send audio echo from WAV file (always echo the audio)
                    await self.connection_handler._send_audio_from_wav(wav_file)
                    
                    # Analyze audio with LLM if available and send text result
                    if self.llm_provider:
                        await self._analyze_audio_and_send_text(wav_file)
                    else:
                        # Send default echo text
                        await self._send_text_tts_sequence("（回声）")
                else:
                    self.logger.error("Failed to save WAV file, cannot send echo")
            else:
                self.logger.warning("Speech too short, ignoring")
        
        # 注意：不再累积到audio_buffer，避免多轮对话中的空白累积问题


    async def handle_text_input(self, text: str) -> None:
        """Process text input with GPT-4.1 analysis if available."""
        self.logger.info(f"Processing text input: {text}")
        
        if self.llm_provider:
            # Analyze text with LLM
            await self._analyze_and_respond_text(text)
        else:
            # Fallback: echo back the received text
            self.logger.info(f"Echoing back text: {text}")
            await self._send_text_tts_sequence(text)

    async def _send_text_tts_sequence(self, text: str) -> None:
        """Send TTS sequence for text response."""
        # Send STT + TTS start messages
        await self.send_text_response(text)
        
        # Send TTS sentence messages with the text
        from core.protocol import protocol_handler, TTSState
        tts_sentence_start = protocol_handler.create_tts_message(self.session_id, TTSState.SENTENCE_START, text)
        self.logger.debug(f"Sending TTS sentence_start: {tts_sentence_start}")
        await self.websocket.send(protocol_handler.serialize_message(tts_sentence_start))
        
        tts_sentence_end = protocol_handler.create_tts_message(self.session_id, TTSState.SENTENCE_END, text) 
        self.logger.debug(f"Sending TTS sentence_end: {tts_sentence_end}")
        await self.websocket.send(protocol_handler.serialize_message(tts_sentence_end))
        
        # Send TTS stop message to complete the sequence
        tts_stop = protocol_handler.create_tts_message(self.session_id, TTSState.STOP)
        self.logger.debug(f"Sending TTS stop: {tts_stop}")
        await self.websocket.send(protocol_handler.serialize_message(tts_stop))

    async def send_complete_audio(self) -> None:
        """Force completion of current audio session (called when listen stop received)."""
        # Check if VAD has collected audio packets
        if self.vad.audio_packets and len(self.vad.audio_packets) > 10:
            self.logger.info(f"Forced audio completion - processing {len(self.vad.audio_packets)} VAD packets")
            
            # Save VAD audio as WAV file
            wav_file = self.audio_saver.save_audio_as_wav(self.vad.audio_packets, self.session_id)
            if wav_file:
                self.logger.info(f"Forced completion - audio saved to: {wav_file}")
                # Send audio echo from WAV file (always echo the audio)
                await self.connection_handler._send_audio_from_wav(wav_file)
                
                # Analyze audio with LLM if available and send text result
                if self.llm_provider:
                    await self._analyze_audio_and_send_text(wav_file)
                else:
                    # Send default echo text
                    await self._send_text_tts_sequence("（回声）")
            else:
                self.logger.error("Forced completion - failed to save WAV file")
                
            # Reset VAD state
            self.vad.reset()
        elif self.audio_buffer:
            # Fallback to old buffer method if VAD has no packets
            self.logger.warning(f"Forced completion - using fallback buffer method ({len(self.audio_buffer)} bytes)")
            await self.send_audio_echo()
        else:
            self.logger.warning("Forced completion called but no audio data available")

    async def send_audio_echo(self) -> None:
        """Send the buffered audio back to the client."""
        if self.audio_buffer:
            self.logger.info(f"Echoing back {len(self.audio_buffer)} bytes of audio.")
            # Since this is echo service, we don't have the original text, so pass None
            await self.send_audio_response(bytes(self.audio_buffer), text=None)
            self.audio_buffer.clear()
        else:
            self.logger.warning("send_audio_echo called but buffer is empty.")

    async def cleanup(self) -> None:
        """Clean up resources and end monitoring session."""
        if self.session_id:
            self.monitor.end_session(self.session_id, completed=True)
        self.audio_buffer.clear()
        self.vad.reset()
        self.logger.info("Cleaned up resources.")

    async def _analyze_audio_and_send_text(self, wav_file_path: str) -> None:
        """Analyze audio with LLM and send text result only."""
        try:
            system_prompt = """分析这段语音内容，请回答以下问题：
1. 有几个人在说话？
2. 能听出是在什么环境（例如办公室、大街上、机场、海边等）下说话的吗？ 
3. 每个人分别在说什么？

请用简洁的中文回答，格式如下：
说话人数：X人
环境：描述环境
说话内容：[具体内容]"""

            user_context = "请分析这段音频"
            
            # Collect streaming response
            response_text = ""
            def stream_callback(chunk: str, is_final: bool) -> bool:
                nonlocal response_text
                response_text += chunk
                return True  # Continue streaming
            
            await self.llm_provider.generate_with_callback(
                system_prompt=system_prompt,
                user_context=user_context,
                file_path=wav_file_path,
                callback=stream_callback
            )
            
            if response_text.strip():
                self.logger.info(f"LLM audio analysis completed")
                await self._send_text_tts_sequence(response_text.strip())
            else:
                self.logger.error("LLM returned empty response")
                await self._send_text_tts_sequence("抱歉，语音分析失败了")
                
        except Exception as e:
            self.logger.error(f"Error in audio analysis: {str(e)}")
            await self._send_text_tts_sequence("抱歉，语音分析出现错误")

    async def _analyze_and_respond_text(self, text: str) -> None:
        """Analyze text with LLM and send response."""
        try:
            system_prompt = "请简洁地分析用户输入的文本内容，并用中文回复。"
            
            # Collect streaming response
            response_text = ""
            def stream_callback(chunk: str, is_final: bool) -> bool:
                nonlocal response_text
                response_text += chunk
                return True  # Continue streaming
            
            await self.llm_provider.generate_with_callback(
                system_prompt=system_prompt,
                user_context=text,
                callback=stream_callback
            )
            
            if response_text.strip():
                self.logger.info(f"LLM text analysis completed")
                await self._send_text_tts_sequence(response_text.strip())
            else:
                self.logger.error("LLM returned empty response")
                await self._send_text_tts_sequence("抱歉，文本分析失败了")
                
        except Exception as e:
            self.logger.error(f"Error in text analysis: {str(e)}")
            await self._send_text_tts_sequence("抱歉，文本分析出现错误")