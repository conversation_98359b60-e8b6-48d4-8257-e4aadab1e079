from typing import Dict, Any
import os
from .base_service import BaseService
from .audio_frontend.vad import SileroVAD
from .audio_frontend.audio_saver import AudioSaver

class EchoService(BaseService):
    """Echo service that returns user's audio back after they finish speaking."""
    
    def __init__(self, config: Dict[str, Any], connection_handler=None):
        super().__init__(config, connection_handler)
        self.audio_buffer = bytearray()
        self.vad = SileroVAD(config)
        # Initialize audio saver with output directory from config or use default
        audio_config = config.get('audio', {})
        output_dir = audio_config.get('output_dir', 'log/audio')
        self.audio_saver = AudioSaver(output_dir)
        
    async def initialize(self) -> bool:
        """Initialize the echo service"""
        self.logger.info("Initialized")
        return True
        
    async def handle_audio_input(self, audio_data: bytes) -> None:
        """Process audio data with VAD detection."""
        # 使用VAD检测语音活动
        vad_result = self.vad.process_audio_frame(audio_data)
        
        if vad_result['speech_end']:
            # 检测到语音结束，保存音频文件并回声
            opus_packets = vad_result['audio_buffer']
            if opus_packets and len(opus_packets) > 10:  # 确保有足够的音频包
                self.logger.info(f"Speech ended, processing {len(opus_packets)} Opus packets")
                
                # Save as WAV file
                wav_file = self.audio_saver.save_audio_as_wav(opus_packets, self.session_id)
                if wav_file:
                    self.logger.info(f"Audio saved to: {wav_file}")
                    
                    # Send audio echo from WAV file
                    await self.connection_handler._send_audio_from_wav(wav_file)
                else:
                    self.logger.error("Failed to save WAV file, cannot send echo")
            else:
                self.logger.warning("Speech too short, ignoring")
        
        # 保持旧的缓冲行为（用于send_audio_echo方法）
        self.audio_buffer.extend(audio_data)


    async def handle_text_input(self, text: str) -> None:
        """Echo back the received text to the client."""
        self.logger.info(f"Echoing back text: {text}")
        
        # Send STT + TTS start messages
        await self.send_text_response(text)
        
        # Send TTS sentence messages with the text
        from core.protocol import protocol_handler, TTSState
        tts_sentence_start = protocol_handler.create_tts_message(self.session_id, TTSState.SENTENCE_START, text)
        self.logger.debug(f"Sending TTS sentence_start: {tts_sentence_start}")
        await self.websocket.send(protocol_handler.serialize_message(tts_sentence_start))
        
        tts_sentence_end = protocol_handler.create_tts_message(self.session_id, TTSState.SENTENCE_END, text) 
        self.logger.debug(f"Sending TTS sentence_end: {tts_sentence_end}")
        await self.websocket.send(protocol_handler.serialize_message(tts_sentence_end))
        
        # Send TTS stop message to complete the sequence
        tts_stop = protocol_handler.create_tts_message(self.session_id, TTSState.STOP)
        self.logger.debug(f"Sending TTS stop: {tts_stop}")
        await self.websocket.send(protocol_handler.serialize_message(tts_stop))

    async def send_complete_audio(self) -> None:
        """Force completion of current audio session (called when listen stop received)."""
        # Check if VAD has collected audio packets
        if self.vad.audio_packets and len(self.vad.audio_packets) > 10:
            self.logger.info(f"Forced audio completion - processing {len(self.vad.audio_packets)} VAD packets")
            
            # Save VAD audio as WAV file
            wav_file = self.audio_saver.save_audio_as_wav(self.vad.audio_packets, self.session_id)
            if wav_file:
                self.logger.info(f"Forced completion - audio saved to: {wav_file}")
                await self.connection_handler._send_audio_from_wav(wav_file)
            else:
                self.logger.error("Forced completion - failed to save WAV file")
                
            # Reset VAD state
            self.vad.reset()
        elif self.audio_buffer:
            # Fallback to old buffer method if VAD has no packets
            self.logger.warning(f"Forced completion - using fallback buffer method ({len(self.audio_buffer)} bytes)")
            await self.send_audio_echo()
        else:
            self.logger.warning("Forced completion called but no audio data available")

    async def send_audio_echo(self) -> None:
        """Send the buffered audio back to the client."""
        if self.audio_buffer:
            self.logger.info(f"Echoing back {len(self.audio_buffer)} bytes of audio.")
            # Since this is echo service, we don't have the original text, so pass None
            await self.send_audio_response(bytes(self.audio_buffer), text=None)
            self.audio_buffer.clear()
        else:
            self.logger.warning("send_audio_echo called but buffer is empty.")

    async def cleanup(self) -> None:
        """Clean up resources and end monitoring session."""
        if self.session_id:
            self.monitor.end_session(self.session_id, completed=True)
        self.audio_buffer.clear()
        self.vad.reset()
        self.logger.info("Cleaned up resources.")