#!/usr/bin/env python3
import asyncio
import os
from typing import Dict, Any, Optional
from google import genai
from google.genai import types
from .base_service import BaseService

class GeminiLiveService(BaseService):
    """Gemini Live service for real-time audio conversation"""
    
    def __init__(self, config: Dict[str, Any], connection_handler=None):
        super().__init__(config, connection_handler)
        self.gemini_session = None
        self.gemini_task = None
        self.audio_buffer = bytearray()
        self.client = None
        
    async def initialize(self) -> bool:
        """Initialize Gemini Live connection"""
        try:
            # Get API key from config or environment
            api_key = self.config.get('gemini', {}).get('api_key') or os.getenv("GOOGLE_API_KEY")
            if not api_key:
                self.logger.error("[GEMINI SERVICE] Error: GOOGLE_API_KEY not found in config.gemini.api_key or environment")
                return False
                
            os.environ["GOOGLE_API_KEY"] = api_key
            
            # Create client
            self.client = genai.Client(api_key=api_key)
            model = self.config.get("gemini", {}).get("model", "gemini-2.5-flash-preview-native-audio-dialog")
            
            # Use correct config format for Gemini Live API
            gemini_config = {
                "response_modalities": ["AUDIO"],
                "system_instruction": "You are a helpful assistant."
            }
            
            # Connect to Gemini
            session = await self.client.aio.live.connect(model=model, config=gemini_config).__aenter__()
            self.gemini_session = session
            
            # Start response handler
            self.gemini_task = asyncio.create_task(self._handle_gemini_response())
            
            self.logger.info("[GEMINI SERVICE] Connected to Gemini successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"[GEMINI SERVICE] Failed to initialize: {e}")
            return False
            
    async def handle_audio_input(self, audio_data: bytes) -> None:
        """Collect audio data in buffer"""
        self.audio_buffer.extend(audio_data)
        self.logger.debug(f"[GEMINI SERVICE] Audio buffer size: {len(self.audio_buffer)} bytes")
        
    async def handle_text_input(self, text: str) -> None:
        """Handle text input (could be used for text-to-speech)"""
        self.logger.info(f"[GEMINI SERVICE] Received text: {text}")
        
        # Record request start for latency monitoring
        if self.session_id:
            self.monitor.record_request_start(self.session_id)
            # Estimate input tokens (rough approximation: 1 token ≈ 4 characters)
            estimated_tokens = len(text) // 4
            self.monitor.record_token_usage(self.session_id, input_tokens=estimated_tokens)
            
        if self.gemini_session:
            try:
                await self.gemini_session.send_realtime_input(text=text)
                self.logger.info("[GEMINI SERVICE] Text sent to Gemini")
            except Exception as e:
                self.logger.error(f"[GEMINI SERVICE] Error sending text: {e}")
                
    async def send_complete_audio(self):
        """Send complete audio buffer to Gemini when recording stops"""
        if self.gemini_session and len(self.audio_buffer) > 0:
            try:
                # Record request start for latency monitoring
                if self.session_id:
                    self.monitor.record_request_start(self.session_id)
                
                self.logger.info(f"[GEMINI SERVICE] Sending complete audio buffer: {len(self.audio_buffer)} bytes")
                audio_blob = types.Blob(
                    data=bytes(self.audio_buffer), 
                    mime_type="audio/pcm;rate=16000"
                )
                await self.gemini_session.send_realtime_input(audio=audio_blob)
                self.logger.info("[GEMINI SERVICE] Complete audio sent to Gemini")
                self.audio_buffer.clear()
            except Exception as e:
                self.logger.error(f"[GEMINI SERVICE] Error sending audio to Gemini: {e}")
        else:
            self.logger.info("[GEMINI SERVICE] No audio data to send to Gemini")
            
    async def _handle_gemini_response(self):
        """Handle responses from Gemini and forward to client"""
        try:
            async for response in self.gemini_session.receive():
                # Handle text response
                if hasattr(response, 'text') and response.text:
                    await self.send_text_response(response.text)
                    self.logger.info(f"[GEMINI SERVICE] Sent text response: {response.text}")
                    
                    # Record output tokens for monitoring
                    if self.session_id:
                        estimated_tokens = len(response.text) // 4
                        self.monitor.record_token_usage(self.session_id, output_tokens=estimated_tokens)
                
                # Handle audio response
                if hasattr(response, 'data') and response.data:
                    await self.send_audio_response(response.data)
                    self.logger.info(f"[GEMINI SERVICE] Sent audio response: {len(response.data)} bytes")
                    
        except Exception as e:
            self.logger.error(f"[GEMINI SERVICE] Error handling Gemini response: {e}")
            # Reset session on error
            self.gemini_session = None
            
    async def cleanup(self) -> None:
        """Cleanup Gemini resources"""
        self.logger.info("[GEMINI SERVICE] Cleaning up...")
        
        # End monitoring session
        if self.session_id:
            self.monitor.end_session(self.session_id, completed=True)
        
        if self.gemini_task:
            self.gemini_task.cancel()
            try:
                await self.gemini_task
            except asyncio.CancelledError:
                pass
                
        if self.gemini_session:
            try:
                await self.gemini_session.__aexit__(None, None, None)
            except Exception as e:
                self.logger.error(f"[GEMINI SERVICE] Error closing session: {e}")
                
        self.gemini_session = None
        self.gemini_task = None
        self.audio_buffer.clear()
        self.logger.info("[GEMINI SERVICE] Cleanup completed")