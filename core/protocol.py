import json
from typing import Dict, Any, Optional
from dataclasses import dataclass
from logger import get_logger

logger = get_logger(__name__)

@dataclass
class AudioParams:
    """Audio parameters for WebSocket communication"""
    format: str = "opus"
    sample_rate: int = 24000
    channels: int = 1
    frame_duration: int = 60

class MessageType:
    """Message types for WebSocket communication"""
    HELLO = "hello"
    LISTEN = "listen"
    TEXT = "text"
    ABORT = "abort"
    STT = "stt"
    TTS = "tts"

class ListenState:
    """Listen states for audio recording"""
    START = "start"
    STOP = "stop"
    DETECT = "detect"

class TTSState:
    """TTS states for audio playback"""
    START = "start"
    STOP = "stop"
    SENTENCE_START = "sentence_start"
    SENTENCE_END = "sentence_end"

class ProtocolHandler:
    """Handles WebSocket communication protocol"""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
    
    def create_hello_response(self, session_id: str, audio_params: Optional[AudioParams] = None) -> Dict[str, Any]:
        """Create hello response message"""
        if audio_params is None:
            audio_params = AudioParams()
            
        response = {
            "type": MessageType.HELLO,
            "transport": "websocket",
            "session_id": session_id,
            "audio_params": {
                "format": audio_params.format,
                "sample_rate": audio_params.sample_rate,
                "channels": audio_params.channels,
                "frame_duration": audio_params.frame_duration
            }
        }
        return response
    
    def create_stt_message(self, session_id: str, text: str) -> Dict[str, Any]:
        """Create STT (Speech-to-Text) message"""
        return {
            "type": MessageType.STT,
            "text": text,
            "session_id": session_id
        }
    
    def create_tts_message(self, session_id: str, state: str, text: Optional[str] = None) -> Dict[str, Any]:
        """Create TTS (Text-to-Speech) message"""
        message = {
            "type": MessageType.TTS,
            "state": state,
            "session_id": session_id
        }
        if text is not None:
            message["text"] = text
        return message
    
    def parse_message(self, message: str) -> Optional[Dict[str, Any]]:
        """Parse incoming JSON message"""
        try:
            data = json.loads(message)
            self.logger.debug(f"Parsed message: {data}")
            return data
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse JSON message: {e}")
            return None
    
    def validate_hello_message(self, data: Dict[str, Any]) -> bool:
        """Validate hello message format"""
        return (
            data.get("type") == MessageType.HELLO and
            isinstance(data.get("version"), (str, type(None)))
        )
    
    def validate_listen_message(self, data: Dict[str, Any]) -> bool:
        """Validate listen message format"""
        return (
            data.get("type") == MessageType.LISTEN and
            data.get("state") in [ListenState.START, ListenState.STOP]
        )
    
    def validate_text_message(self, data: Dict[str, Any]) -> bool:
        """Validate text message format"""
        return (
            data.get("type") == MessageType.TEXT and
            isinstance(data.get("text"), str)
        )
    
    def validate_abort_message(self, data: Dict[str, Any]) -> bool:
        """Validate abort message format"""
        return (
            data.get("type") == MessageType.ABORT and
            isinstance(data.get("reason"), (str, type(None)))
        )
    
    def get_message_type(self, data: Dict[str, Any]) -> Optional[str]:
        """Get message type from parsed data"""
        return data.get("type")
    
    def get_listen_state(self, data: Dict[str, Any]) -> Optional[str]:
        """Get listen state from listen message"""
        if data.get("type") == MessageType.LISTEN:
            return data.get("state")
        return None
    
    def get_text_content(self, data: Dict[str, Any]) -> Optional[str]:
        """Get text content from text message"""
        if data.get("type") == MessageType.TEXT:
            return data.get("text")
        return None
    
    def get_abort_reason(self, data: Dict[str, Any]) -> Optional[str]:
        """Get abort reason from abort message"""
        if data.get("type") == MessageType.ABORT:
            return data.get("reason", "unknown")
        return None
    
    def serialize_message(self, message: Dict[str, Any]) -> str:
        """Serialize message to JSON string"""
        try:
            return json.dumps(message)
        except Exception as e:
            self.logger.error(f"Failed to serialize message: {e}")
            raise

# Global protocol handler instance
protocol_handler = ProtocolHandler()