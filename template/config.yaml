# WebSocket Server Configuration

# Service provider settings (echo, gemini_live)
service:
  type: "echo"  # Options: "echo", "gemini_live"

# Server settings
server:
  host: "0.0.0.0"
  port: 8001
  path: "/xiaozhi/v1"

# Gemini settings
gemini:
  api_key: "your gemini key here"
  model: "gemini-2.5-flash"

openai:
  api_key: "your openai key here"
  model: "gpt-4.1"

# Audio settings
audio:
  format: "opus"
  sample_rate: 16000
  channels: 1
  frame_duration: 60
  output_dir: "log/audio"  # Directory to store audio files

# VAD (Voice Activity Detection) settings
vad:
  threshold: 0.5  # Silero VAD模型的语音检测阈值，范围0-1，越小越敏感
  silence_duration_ms: 500  # 从 Websocket 接收语音流，连续多少毫秒没有语音，就认为用户说的一句话结束了，交给后续处理
  max_speech_duration_ms: 15000  # 连续接收语音流超过多少毫秒没有截断，强行截断为一个wav，以防止背景声音杂乱时一直停不下来
